# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a production-ready SaaS starter template built with Remix running entirely on Cloudflare Workers and Neon PostgreSQL. The project uses a unified single-Worker architecture designed for maximum performance, scalability, and cost efficiency on Cloudflare's edge network.

## Development Commands

### Build System
- `pnpm build` - Build all apps and packages
- `pnpm dev` - Start development servers for all apps
- `pnpm lint` - Run Biome linting across all packages
- `pnpm format` - Format code with Biome
- `pnpm check-types` - Run TypeScript type checking

### Database Commands
- `pnpm db:push` - Push database schema changes to development
- `pnpm db:generate` - Generate database migrations
- `pnpm db:migrate` - Run database migrations
- `pnpm db:studio` - Open Drizzle Studio for database management

### Filtered Commands (Turbo)
- `pnpm turbo dev --filter=web` - Start the main Worker application
- `pnpm turbo build --filter=web` - Build the main Worker application
- `pnpm turbo build --filter=ui-kit` - Build only the UI kit package

### Worker Application Commands
- **Main app**: `cd apps/web && pnpm dev` (runs on port 5173)
- **Build**: `cd apps/web && pnpm build`
- **Deploy**: `cd apps/web && pnpm deploy`
- **Preview**: `cd apps/web && pnpm preview`

## Architecture

### Monorepo Structure
```
├── apps/
│   └── web/        # Single Remix Worker Application
│       ├── app/    # Remix application code
│       │   ├── routes/     # File-based routing + API routes
│       │   ├── lib/        # Application utilities
│       │   └── components/ # React components
│       ├── build/          # Build output
│       │   ├── client/     # Static assets
│       │   └── server/     # SSR bundle
│       ├── worker.js       # Worker entry point
│       └── wrangler.toml   # Worker configuration
└── packages/
    ├── auth/       # BetterAuth authentication system
    ├── db/         # Database schemas and migrations (Drizzle ORM)
    ├── config/     # Environment configuration management
    ├── ui-kit/     # React component library with Radix UI
    ├── shared/     # Common utilities, types, and constants
    ├── billing/    # Stripe payment integration
    ├── storage/    # Cloudflare R2 and S3 file storage
    ├── mailer/     # Email functionality
    ├── monitoring/ # Logging and error tracking
    └── [others]/   # Additional feature packages
```

### Key Technologies
- **Runtime**: Cloudflare Workers (Single Worker Architecture)
- **Frontend**: Remix 2.16.8 with React 19.1.0
- **Database**: Neon PostgreSQL with Drizzle ORM
- **Authentication**: BetterAuth
- **Styling**: Tailwind CSS with CSS variables
- **Components**: Radix UI primitives
- **Build**: Turborepo with pnpm workspaces
- **Code Quality**: Biome (replaces ESLint + Prettier)
- **TypeScript**: 5.8.2

### Cloudflare Platform Integration
- **Workers**: Main application runtime
- **R2**: Object storage for files and assets
- **KV**: Key-value storage for caching
- **AI**: Machine learning capabilities
- **D1**: Edge SQL database (optional)
- **Analytics**: Performance and usage monitoring

### Package Exports
- `@repo/shared` exports: `./types`, `./constants`, `./utils`
- `@repo/ui-kit` exports: `.`, `./components/*`, `./primitives/*`, `./styles/*`, `./utils`
- `@repo/auth` exports: `.`, `./config`, `./providers`
- `@repo/db` exports: `.`, `./schema`, `./migrations`
- `@repo/config` exports: `.`, `./env`

## Development Workflow

### Adding New Components to UI Kit
Use the component generator: `cd packages/ui-kit && pnpm generate:component`

### Creating New Packages
1. Create directory in `packages/` folder
2. Add package.json with `@repo/` prefix
3. Add to `pnpm-workspace.yaml`
4. Configure exports in package.json
5. Add build configuration to turbo.json

### Package Dependencies
- Use `workspace:*` for internal package dependencies
- All packages use TypeScript 5.8.2 and React 19.1.0
- Biome configured for consistent code formatting and linting
- Strict TypeScript configuration with `noEmit: true`

### Build Pipeline
- Turbo handles build orchestration with dependency awareness
- TypeScript compilation runs in parallel across packages
- Outputs cached for performance (`dist/` directories)
- Supports incremental builds for faster development

### Database Workflow
1. Make schema changes in `packages/db/src/schema.ts`
2. Run `pnpm db:push` for development changes
3. Run `pnpm db:generate` to create migration files
4. Run `pnpm db:migrate` for production deployments

## Important Notes

### Package Manager
This project uses `pnpm` as the package manager. Always use `pnpm` commands, not `npm` or `yarn`.

### Monorepo Conventions
- All internal packages are prefixed with `@repo/`
- Shared TypeScript configurations in `packages/typescript-config`
- Use Turbo filters for targeted builds and development
- Workspace dependencies should use `workspace:*` protocol

### Code Quality
- Biome handles both linting and formatting (no ESLint/Prettier)
- Strict TypeScript configuration with comprehensive type checking
- All packages must build without warnings
- Use TypeScript 5.8.2 features and React 19 patterns

### Deployment Target
- Primary deployment target is Cloudflare Workers (single Worker architecture)
- All functionality (web app, API routes, static assets) runs in one Worker
- Database hosted on Neon PostgreSQL
- Static assets served directly from Worker using Cloudflare's asset handling
- Global deployment across 300+ edge locations

### Authentication
- BetterAuth handles authentication with multiple providers
- Session management integrated with database
- Role-based access control ready for implementation

### Environment Configuration
- Environment variables managed through `@repo/config`
- Type-safe environment variable loading
- Separate configurations for development and production
## Recent Updates (2025-06-30)

### UI/UX Overhaul Completed
- **Tailwind CSS 4.0 Migration**: Upgraded from traditional Tailwind to v4.0 with modern `@theme` configuration
- **shadcn/ui Integration**: Replaced custom components with production-ready shadcn/ui components
- **Page Redesigns**:
  - Landing page: Modern hero section with feature showcase and testimonials
  - Dashboard: Clean, compact interface with improved information density
  - Authentication: Streamlined login/signup with better UX
- **Design System**: Established consistent design tokens and component variants
- **Performance**: Optimized CSS bundle size and improved loading times
- **Accessibility**: Enhanced keyboard navigation and screen reader support

### Component Library Expansion
- Added 10+ new components: Dialog, Alert, Avatar, Navigation Menu, etc.
- Improved type safety with class-variance-authority integration
- Better CSS variable system for theming
- Responsive design optimizations for all screen sizes

### Development Experience
- Improved monorepo CSS architecture with proper package exports
- Better TypeScript integration across all UI components
- Enhanced development workflow with faster builds and hot reloading