import { createRequestHandler } from "@remix-run/cloudflare";
import * as build from "./build/server/index.js";

const handleRequest = createRequestHandler(build, "production");

export default {
  async fetch(request, env, ctx) {
    try {
      return await handleRequest(request, {
        cloudflare: {
          env,
          ctx,
        },
      });
    } catch (error) {
      console.error("Worker error:", error);
      return new Response("Internal Server Error", { status: 500 });
    }
  },
};
