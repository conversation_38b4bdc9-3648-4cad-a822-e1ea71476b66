import type { LoaderFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";

export async function loader({ context }: LoaderFunctionArgs) {
  const env = (context.cloudflare as { env: Record<string, string> }).env;

  return json({
    message: "🚀 Remix + Cloudflare Worker API is running!",
    environment: env.NODE_ENV || "development",
    timestamp: new Date().toISOString(),
    endpoints: {
      health: "/api/health",
      upload: "/api/upload/presigned",
      webhook: "/api/webhook",
    },
  });
}
