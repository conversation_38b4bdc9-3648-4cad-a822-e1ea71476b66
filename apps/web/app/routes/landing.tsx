import type { MetaFunction } from "@remix-run/cloudflare";
import {
    CTAS<PERSON>tion,
    FeatureGrid,
    <PERSON><PERSON>,
    <PERSON>er,
    HeroSection,
    StatsGrid,
    TestimonialGrid
} from "~/components";
import { Lightbulb, Shield, Zap } from "lucide-react";

export const meta: MetaFunction = () => {
    return [
        { title: "Welcome - AI Chat Platform" },
        {
            name: "description",
            content:
                "Experience the future of AI-powered conversations with our intelligent chat platform",
        },
    ];
};

// Features data
const features = [
    {
        icon: Zap,
        title: "Lightning Fast",
        description: "Get instant responses with our optimized AI engine running on Cloudflare Edge",
        iconColor: "text-blue-600",
        iconBgColor: "bg-blue-100",
    },
    {
        icon: Lightbulb,
        title: "Smart & Intuitive",
        description: "Our AI understands context and provides intelligent, relevant responses",
        iconColor: "text-green-600",
        iconBgColor: "bg-green-100",
    },
    {
        icon: Shield,
        title: "Secure & Private",
        description: "Your conversations are protected with enterprise-grade security",
        iconColor: "text-purple-600",
        iconBgColor: "bg-purple-100",
    },
];

// Stats data
const stats = [
    { value: "10K+", label: "Active Users", color: "text-blue-600" },
    { value: "1M+", label: "Messages Processed", color: "text-green-600" },
    { value: "99.9%", label: "Uptime", color: "text-purple-600" },
    { value: "24/7", label: "Available", color: "text-orange-600" },
];

// Testimonials data
const testimonials = [
    {
        name: "John Smith",
        role: "Product Manager",
        avatar: "JS",
        content: "This AI assistant has revolutionized how I work. It's like having a super-smart colleague available 24/7.",
        avatarColor: "bg-blue-500",
    },
    {
        name: "Maria Davis",
        role: "Designer",
        avatar: "MD",
        content: "The interface is intuitive and the responses are incredibly helpful. I use it for everything from brainstorming to research.",
        avatarColor: "bg-green-500",
    },
    {
        name: "Robert Wilson",
        role: "Developer",
        avatar: "RW",
        content: "Fast, reliable, and secure. This platform has become an essential part of my daily workflow.",
        avatarColor: "bg-purple-500",
    },
];

export default function Landing() {
    return (
        <div className="min-h-screen bg-white">
            <Header />

            {/* Hero Section */}
            <HeroSection
                title="The Future of"
                titleHighlight="AI Conversations"
                description="Experience intelligent, context-aware conversations with our advanced AI assistant. Get instant answers, creative solutions, and personalized assistance."
                primaryCTA={{
                    text: "Try AI Chat Now",
                    href: "/",
                }}
                secondaryCTA={{
                    text: "Watch Demo",
                    onClick: () => {
                        // Add demo functionality here
                        console.log("Watch demo clicked");
                    },
                }}
                backgroundGradient="from-blue-600 to-purple-600"
            />

            {/* Features Section */}
            <FeatureGrid
                title="Powerful Features"
                description="Discover what makes our AI assistant the perfect companion for your daily tasks"
                features={features}
                columns={3}
                backgroundColor="bg-gray-50"
            />

            {/* Stats Section */}
            <StatsGrid
                title="Trusted by Thousands"
                description="Join the growing community of users who rely on our AI assistant"
                stats={stats}
                columns={4}
                backgroundColor="bg-white"
            />

            {/* Testimonials Section */}
            <TestimonialGrid
                title="What Our Users Say"
                description="Real feedback from real users"
                testimonials={testimonials}
                columns={3}
                backgroundColor="bg-gray-50"
            />

            {/* CTA Section */}
            <CTASection
                title="Ready to Get Started?"
                description="Join thousands of users who are already experiencing the power of AI-driven conversations"
                primaryCTA={{
                    text: "Start Chatting Now",
                    href: "/",
                }}
                secondaryCTA={{
                    text: "Learn More",
                    href: "/about",
                }}
            />

            <Footer />
        </div>
    );
}
