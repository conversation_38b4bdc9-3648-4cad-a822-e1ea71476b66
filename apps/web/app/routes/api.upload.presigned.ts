import type { ActionFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";

export async function action({ request, context }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    throw new Response("Method not allowed", { status: 405 });
  }

  try {
    const { filename, contentType } = (await request.json()) as {
      filename: string;
      contentType: string;
    };

    if (!filename) {
      throw new Response("Filename is required", { status: 400 });
    }

    // Generate unique filename
    const uniqueFilename = `${Date.now()}-${filename}`;

    // Access R2 bucket from context
    const env = (context.cloudflare as { env: Record<string, any> }).env;
    const bucket = env.R2_BUCKET;

    if (!bucket) {
      throw new Response("R2 bucket not configured", { status: 500 });
    }

    // For now, return a simple upload URL
    // In production, you would create a proper presigned URL using R2
    return json({
      uploadUrl: `/api/upload/${uniqueFilename}`,
      filename: uniqueFilename,
      contentType: contentType || "application/octet-stream",
    });
  } catch (error) {
    console.error("Presigned upload error:", error);
    throw new Response("Invalid request", { status: 400 });
  }
}
