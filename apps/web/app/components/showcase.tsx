import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@repo/ui-kit";
import { Clock, Shield, Users, Zap } from "lucide-react";

const features = [
  {
    icon: Zap,
    title: "Lightning Fast",
    description: "Get instant responses with our optimized AI engine running on Cloudflare Edge",
  },
  {
    icon: Shield,
    title: "Smart & Intuitive",
    description: "Our AI understands context and provides intelligent, relevant responses",
  },
  {
    icon: Users,
    title: "Secure & Private",
    description: "Your conversations are protected with enterprise-grade security",
  },
  {
    icon: Clock,
    title: "24/7 Available",
    description: "Always ready to help whenever you need assistance",
  },
];

const stats = [
  { value: "10K+", label: "Active Users" },
  { value: "1M+", label: "Messages Processed" },
  { value: "99.9%", label: "Uptime" },
  { value: "24/7", label: "Available" },
];

const testimonials = [
  {
    name: "<PERSON>",
    role: "Product Manager",
    avatar: "JS",
    content:
      "This AI assistant has revolutionized how I work. It's like having a super-smart colleague available 24/7.",
  },
  {
    name: "<PERSON>",
    role: "Designer",
    avatar: "<PERSON>",
    content:
      "The interface is intuitive and the responses are incredibly helpful. I use it for everything from brainstorming to research.",
  },
  {
    name: "<PERSON>",
    role: "Developer",
    avatar: "RW",
    content:
      "Fast, reliable, and secure. This platform has become an essential part of my daily workflow.",
  },
  {
    name: "Sarah Johnson",
    role: "Marketing Manager",
    avatar: "SJ",
    content:
      "The AI helps me create better content and strategies. It's an invaluable tool for our team.",
  },
];

export function Showcase() {
  return (
    <div className="space-y-6 p-4">
      {/* Features Section */}
      <section>
        <div className="text-center mb-4">
          <h2 className="text-xl font-bold text-gray-900 mb-1">Powerful Features</h2>
          <p className="text-sm text-gray-600">
            Discover what makes our AI assistant perfect for your daily tasks
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
          {features.map((feature, index) => (
            <Card key={index} className="text-center">
              <CardHeader className="pb-2 pt-4">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                  <feature.icon className="w-5 h-5 text-blue-600" />
                </div>
                <CardTitle className="text-base">{feature.title}</CardTitle>
              </CardHeader>
              <CardContent className="pt-0 pb-4">
                <p className="text-xs text-gray-600">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Stats Section */}
      <section>
        <div className="text-center mb-4">
          <h2 className="text-xl font-bold text-gray-900 mb-1">Trusted by Thousands</h2>
          <p className="text-sm text-gray-600">
            Join the growing community of users who rely on our AI assistant
          </p>
        </div>

        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
          {stats.map((stat, index) => (
            <div key={index} className="text-center p-3 bg-white rounded-lg border">
              <div className="text-2xl font-bold text-blue-600 mb-1">{stat.value}</div>
              <div className="text-xs text-gray-600">{stat.label}</div>
            </div>
          ))}
        </div>
      </section>

      {/* Testimonials Section */}
      <section>
        <div className="text-center mb-4">
          <h2 className="text-xl font-bold text-gray-900 mb-1">What Our Users Say</h2>
          <p className="text-sm text-gray-600">Real feedback from real users</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
          {testimonials.map((testimonial, index) => (
            <Card key={index}>
              <CardHeader className="pb-2 pt-3">
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                    <span className="text-white font-semibold text-xs">{testimonial.avatar}</span>
                  </div>
                  <div>
                    <div className="font-semibold text-xs">{testimonial.name}</div>
                    <div className="text-xs text-gray-600">{testimonial.role}</div>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0 pb-3">
                <p className="text-xs text-gray-600">{testimonial.content}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>
    </div>
  );
}
