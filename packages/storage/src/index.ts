export interface StorageProvider {
  upload(
    key: string,
    data: ArrayBuffer | Uint8Array | string,
    options?: UploadOptions
  ): Promise<UploadResult>;
  download(key: string): Promise<DownloadResult>;
  delete(key: string): Promise<DeleteResult>;
  createPresignedUrl(key: string, expiresIn?: number): Promise<string>;
}

export interface UploadOptions {
  contentType?: string;
  metadata?: Record<string, string>;
}

export interface UploadResult {
  success: boolean;
  key: string;
  url?: string;
  error?: string;
}

export interface DownloadResult {
  success: boolean;
  data?: ArrayBuffer;
  contentType?: string;
  error?: string;
}

export interface DeleteResult {
  success: boolean;
  error?: string;
}

export * from "./r2/index.js";
