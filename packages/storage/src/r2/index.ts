import type {
  DeleteR<PERSON>ult,
  Download<PERSON><PERSON>ult,
  StorageProvider,
  UploadOptions,
  UploadResult,
} from "../index.js";

// Define R2Bucket interface for TypeScript
interface R2Bucket {
  put(
    key: string,
    data: ArrayBuffer | Uint8Array | string,
    options?: {
      httpMetadata?: { contentType?: string };
      customMetadata?: Record<string, string>;
    }
  ): Promise<R2Object | null>;
  get(key: string): Promise<R2Object | null>;
  delete(key: string): Promise<void>;
}

interface R2Object {
  arrayBuffer(): Promise<ArrayBuffer>;
  httpMetadata?: { contentType?: string };
}

export class R2StorageProvider implements StorageProvider {
  constructor(private bucket: R2Bucket) {}

  async upload(
    key: string,
    data: ArrayBuffer | Uint8Array | string,
    options?: UploadOptions
  ): Promise<UploadResult> {
    try {
      await this.bucket.put(key, data, {
        httpMetadata: {
          contentType: options?.contentType,
        },
        customMetadata: options?.metadata,
      });

      return {
        success: true,
        key,
        url: `https://r2.dev/${key}`, // Replace with your R2 domain
      };
    } catch (error) {
      return {
        success: false,
        key,
        error: error instanceof Error ? error.message : "Upload failed",
      };
    }
  }

  async download(key: string): Promise<DownloadResult> {
    try {
      const object = await this.bucket.get(key);

      if (!object) {
        return {
          success: false,
          error: "Object not found",
        };
      }

      return {
        success: true,
        data: await object.arrayBuffer(),
        contentType: object.httpMetadata?.contentType,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Download failed",
      };
    }
  }

  async delete(key: string): Promise<DeleteResult> {
    try {
      await this.bucket.delete(key);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Delete failed",
      };
    }
  }

  async createPresignedUrl(key: string, expiresIn = 3600): Promise<string> {
    // Note: R2 presigned URLs require additional setup
    // This is a placeholder implementation
    return `https://r2.dev/${key}?expires=${Date.now() + expiresIn * 1000}`;
  }
}
