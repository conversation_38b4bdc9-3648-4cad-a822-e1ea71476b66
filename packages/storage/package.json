{"name": "@repo/storage", "version": "0.1.0", "type": "module", "private": true, "exports": {".": "./src/index.js", "./r2": "./src/r2/index.js", "./s3": "./src/s3/index.js"}, "scripts": {"dev": "tsc --watch", "build": "tsc", "check-types": "tsc --noEmit", "lint": "biome check .", "lint:fix": "biome check --write ."}, "dependencies": {"@repo/shared": "workspace:*"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@types/node": "^22.15.3", "typescript": "5.8.2"}}