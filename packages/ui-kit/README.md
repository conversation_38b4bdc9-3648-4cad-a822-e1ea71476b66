# UI组件库 (@repo/ui-kit)

这是一个基于 Tailwind CSS 和 Radix UI 的现代化组件库，为整个应用提供一致的设计系统。

## 特性

- ✅ **TypeScript**: 完整的类型支持
- ✅ **Tailwind CSS**: 基于实用程序的样式系统
- ✅ **Radix UI**: 无障碍的底层原语
- ✅ **CVA**: 类型安全的变体管理
- ✅ **树摇优化**: 支持按需导入
- ✅ **主题支持**: 内置暗色/亮色主题
- ✅ **ESM**: 现代模块系统

## 组件列表

### 基础组件

- **Button**: 多种变体的按钮组件
- **Input**: 表单输入组件
- **Label**: 表单标签组件
- **Badge**: 徽章组件
- **Card**: 卡片容器组件系列

### 布局组件

- **Card**: 包含 Header, Content, Footer, Title, Description, Action

## 安装和使用

```bash
# 已在 monorepo 中自动安装
pnpm install
```

### 在 Remix 应用中使用

```tsx
import { Button, Card, CardHeader, CardTitle, CardContent } from "@repo/ui-kit";

export default function MyPage() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Hello World</CardTitle>
      </CardHeader>
      <CardContent>
        <Button variant="default" size="lg">
          Click me
        </Button>
      </CardContent>
    </Card>
  );
}
```

### 样式配置

在你的应用中导入基础样式：

```css
/* app/styles/globals.css */
@import "@repo/ui-kit/src/styles/globals.css";
```

在 Tailwind 配置中继承预设：

```js
// tailwind.config.js
const uiKitConfig = require("@repo/ui-kit/tailwind.config.js");

module.exports = {
  ...uiKitConfig,
  content: [
    "./app/**/*.{js,ts,jsx,tsx}",
    "./packages/ui-kit/src/**/*.{js,ts,jsx,tsx}",
  ],
};
```

## 组件 API

### Button

```tsx
interface ButtonProps {
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  size?: "default" | "sm" | "lg" | "icon";
  asChild?: boolean;
}
```

### Card

```tsx
// 复合组件
<Card>
  <CardHeader>
    <CardTitle>标题</CardTitle>
    <CardDescription>描述</CardDescription>
    <CardAction>操作按钮</CardAction>
  </CardHeader>
  <CardContent>内容</CardContent>
  <CardFooter>底部</CardFooter>
</Card>
```

### Badge

```tsx
interface BadgeProps {
  variant?: "default" | "secondary" | "destructive" | "outline";
  asChild?: boolean;
}
```

## 设计Token

所有组件都基于CSS变量系统，支持主题切换：

```css
:root {
  --primary: 240 5.9% 10%;
  --secondary: 240 4.8% 95.9%;
  --destructive: 0 84.2% 60.2%;
  --border: 240 5.9% 90%;
  --radius: 0.5rem;
  /* ... 更多变量 */
}
```

## 工具函数

```tsx
import { cn, formatBytes, debounce, throttle } from "@repo/ui-kit/utils";

// 类名合并
cn("base-class", condition && "conditional-class");

// 文件大小格式化
formatBytes(1024); // "1 KB"

// 防抖
const debouncedFn = debounce(() => console.log("Hello"), 300);

// 节流
const throttledFn = throttle(() => console.log("Hello"), 300);
```

## 开发

```bash
# 开发模式
pnpm dev

# 构建
pnpm build

# 类型检查
pnpm check-types

# 代码检查
pnpm lint
```

## 扩展

要添加新组件：

1. 在 `src/components/` 下创建新文件夹
2. 添加组件文件 (`.tsx`)
3. 创建 `index.ts` 导出
4. 在主 `src/index.ts` 中添加导出

## 最佳实践

1. **使用 forwardRef**: 所有组件都应该支持 ref 转发
2. **类型安全**: 充分利用 TypeScript 和 CVA 的类型推导
3. **可访问性**: 基于 Radix UI 确保无障碍支持
4. **一致性**: 遵循现有的命名和结构约定
5. **性能**: 支持树摇和按需导入
