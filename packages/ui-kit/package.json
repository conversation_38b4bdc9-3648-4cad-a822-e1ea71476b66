{"name": "@repo/ui-kit", "version": "0.1.0", "type": "module", "private": true, "exports": {".": "./dist/index.js", "./components/*": "./dist/components/*/index.js", "./primitives/*": "./dist/primitives/*/index.js", "./styles/*": "./src/styles/*", "./utils": "./dist/utils/index.js"}, "types": "./dist/index.d.ts", "scripts": {"dev": "tsc --watch", "build": "tsc", "lint": "biome check .", "lint:fix": "biome check --write .", "check-types": "tsc --noEmit", "generate:component": "turbo gen react-component"}, "dependencies": {"@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.468.0", "react": "^18.0.0", "react-dom": "^18.0.0", "tailwind-merge": "^2.5.4"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@repo/typescript-config": "workspace:*", "@turbo/gen": "^2.5.0", "@types/node": "^22.15.3", "@types/react": "18.3.17", "@types/react-dom": "18.3.5", "tailwindcss": "^4.0.0", "typescript": "5.8.2"}, "peerDependencies": {"react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0"}}