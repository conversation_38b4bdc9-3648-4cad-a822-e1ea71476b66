import * as React from "react";
import { cn } from "../../utils/index.js";

/**
 * Loading spinner component
 */

export interface SpinnerProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: "sm" | "md" | "lg" | "xl";
  variant?: "primary" | "secondary" | "white";
}

export const Spinner = React.forwardRef<HTMLDivElement, SpinnerProps>(
  ({ className, size = "md", variant = "primary", ...props }, ref) => {
    const sizeClasses = {
      sm: "h-4 w-4",
      md: "h-6 w-6",
      lg: "h-8 w-8",
      xl: "h-12 w-12",
    };

    const variantClasses = {
      primary: "text-primary",
      secondary: "text-muted-foreground",
      white: "text-white",
    };

    return (
      <div
        ref={ref}
        className={cn(
          "animate-spin rounded-full border-2 border-current border-t-transparent",
          sizeClasses[size],
          variantClasses[variant],
          className
        )}
        {...props}
      >
        <span className="sr-only">Loading...</span>
      </div>
    );
  }
);
Spinner.displayName = "Spinner";

export interface LoadingProps {
  children?: React.ReactNode;
  loading?: boolean;
  spinner?: React.ComponentProps<typeof Spinner>;
  className?: string;
}

export const Loading: React.FC<LoadingProps> = ({
  children,
  loading = false,
  spinner,
  className,
}) => {
  if (loading) {
    return (
      <div className={cn("flex items-center justify-center p-4", className)}>
        <Spinner {...spinner} />
      </div>
    );
  }

  return <>{children}</>;
};
