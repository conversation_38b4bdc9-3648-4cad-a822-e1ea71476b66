import * as React from "react";
import { cn } from "../../utils/index.js";

export interface EnhancedInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  icon?: React.ReactNode;
  error?: string;
  success?: boolean;
}

const EnhancedInput = React.forwardRef<HTMLInputElement, EnhancedInputProps>(
  ({ className, type, icon, error, success, ...props }, ref) => {
    const [isFocused, setIsFocused] = React.useState(false);
    const [hasValue, setHasValue] = React.useState(false);

    React.useEffect(() => {
      setHasValue(Boolean(props.value || props.defaultValue));
    }, [props.value, props.defaultValue]);

    return (
      <div className="relative">
        <div className="relative">
          {icon && (
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-500 dark:text-slate-400 transition-colors duration-200">
              {icon}
            </div>
          )}
          <input
            type={type}
            data-slot="enhanced-input"
            className={cn(
              "file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground",
              "flex h-11 w-full min-w-0 rounded-lg border bg-white dark:bg-slate-800 px-3 py-3 text-base shadow-sm transition-all duration-200 outline-none",
              "file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium",
              "disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50",
              "focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20",
              // Icon spacing
              icon && "pl-10",
              // Error state
              error && "border-red-500 focus:border-red-500 focus:ring-red-500/20",
              // Success state
              success && "border-green-500 focus:border-green-500 focus:ring-green-500/20",
              // Default border
              !error && !success && "border-slate-300 dark:border-slate-600",
              // Text color
              "text-slate-900 dark:text-slate-100",
              // Placeholder
              "placeholder:text-slate-500 dark:placeholder:text-slate-400",
              // Hover state
              "hover:border-slate-400 dark:hover:border-slate-500",
              className
            )}
            ref={ref}
            onFocus={(e) => {
              setIsFocused(true);
              props.onFocus?.(e);
            }}
            onBlur={(e) => {
              setIsFocused(false);
              props.onBlur?.(e);
            }}
            onChange={(e) => {
              setHasValue(Boolean(e.target.value));
              props.onChange?.(e);
            }}
            {...props}
          />

          {/* Focus indicator */}
          <div
            className={cn(
              "absolute inset-0 rounded-lg pointer-events-none transition-all duration-200",
              isFocused && "ring-2 ring-blue-500/20 border border-blue-500",
              error && isFocused && "ring-2 ring-red-500/20 border border-red-500",
              success && isFocused && "ring-2 ring-green-500/20 border border-green-500"
            )}
          />
        </div>

        {/* Error message */}
        {error && (
          <p className="mt-2 text-sm text-red-600 dark:text-red-400 flex items-center">
            <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.502 0L4.312 15.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
            {error}
          </p>
        )}
      </div>
    );
  }
);

EnhancedInput.displayName = "EnhancedInput";

export { EnhancedInput };
