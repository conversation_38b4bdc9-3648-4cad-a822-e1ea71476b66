/**
 * Structured logging utilities for Cloudflare Workers
 */

export type LogLevel = "debug" | "info" | "warn" | "error" | "fatal";

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context?: Record<string, unknown>;
  error?: {
    name: string;
    message: string;
    stack?: string;
  };
  requestId?: string;
  userId?: string;
  accountId?: string;
  duration?: number;
}

export interface LoggerConfig {
  level: LogLevel;
  service: string;
  environment: string;
  version?: string;
}

export class Logger {
  private static instance: Logger;
  private config: LoggerConfig;
  private context: Record<string, unknown> = {};

  constructor(config: LoggerConfig) {
    this.config = config;
  }

  static getInstance(config?: LoggerConfig): Logger {
    if (!Logger.instance && config) {
      Logger.instance = new Logger(config);
    }
    return Logger.instance;
  }

  /**
   * Set global context that will be included in all log entries
   */
  setContext(context: Record<string, unknown>): void {
    this.context = { ...this.context, ...context };
  }

  /**
   * Clear global context
   */
  clearContext(): void {
    this.context = {};
  }

  /**
   * Create a child logger with additional context
   */
  child(context: Record<string, unknown>): Logger {
    const childLogger = new Logger(this.config);
    childLogger.context = { ...this.context, ...context };
    return childLogger;
  }

  private shouldLog(level: LogLevel): boolean {
    const levels: Record<LogLevel, number> = {
      debug: 0,
      info: 1,
      warn: 2,
      error: 3,
      fatal: 4,
    };

    return levels[level] >= levels[this.config.level];
  }

  private createLogEntry(
    level: LogLevel,
    message: string,
    context?: Record<string, unknown>,
    error?: Error
  ): LogEntry {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      context: {
        service: this.config.service,
        environment: this.config.environment,
        version: this.config.version,
        ...this.context,
        ...context,
      },
    };

    if (error) {
      entry.error = {
        name: error.name,
        message: error.message,
        stack: error.stack,
      };
    }

    return entry;
  }

  private output(entry: LogEntry): void {
    // For Cloudflare Workers, use console.log with structured JSON
    console.log(JSON.stringify(entry));
  }

  debug(message: string, context?: Record<string, unknown>): void {
    if (this.shouldLog("debug")) {
      this.output(this.createLogEntry("debug", message, context));
    }
  }

  info(message: string, context?: Record<string, unknown>): void {
    if (this.shouldLog("info")) {
      this.output(this.createLogEntry("info", message, context));
    }
  }

  warn(message: string, context?: Record<string, unknown>): void {
    if (this.shouldLog("warn")) {
      this.output(this.createLogEntry("warn", message, context));
    }
  }

  error(message: string, context?: Record<string, unknown>, error?: Error): void {
    if (this.shouldLog("error")) {
      this.output(this.createLogEntry("error", message, context, error));
    }
  }

  fatal(message: string, context?: Record<string, unknown>, error?: Error): void {
    if (this.shouldLog("fatal")) {
      this.output(this.createLogEntry("fatal", message, context, error));
    }
  }

  /**
   * Time a function execution and log the duration
   */
  async time<T>(name: string, fn: () => Promise<T>, context?: Record<string, unknown>): Promise<T> {
    const start = Date.now();
    try {
      const result = await fn();
      const duration = Date.now() - start;
      this.info(`${name} completed`, {
        ...context,
        duration,
        operation: name,
      });
      return result;
    } catch (error) {
      const duration = Date.now() - start;
      this.error(
        `${name} failed`,
        {
          ...context,
          duration,
          operation: name,
        },
        error as Error
      );
      throw error;
    }
  }

  /**
   * Log request information
   */
  logRequest(request: Request, context?: Record<string, unknown>): void {
    this.info("Request received", {
      method: request.method,
      url: request.url,
      userAgent: request.headers.get("user-agent"),
      referer: request.headers.get("referer"),
      ip: request.headers.get("cf-connecting-ip") || request.headers.get("x-forwarded-for"),
      ...context,
    });
  }

  /**
   * Log response information
   */
  logResponse(response: Response, duration: number, context?: Record<string, unknown>): void {
    this.info("Response sent", {
      status: response.status,
      duration,
      ...context,
    });
  }
}

/**
 * Create a logger instance from environment variables
 */
export function createLogger(env: Record<string, string | undefined>): Logger {
  return Logger.getInstance({
    level: (env.LOG_LEVEL as LogLevel) || "info",
    service: env.SERVICE_NAME || "unknown",
    environment: env.NODE_ENV || "development",
    version: env.SERVICE_VERSION,
  });
}

/**
 * Request logging middleware
 */
export function createLoggingMiddleware(logger: Logger) {
  return async (request: Request, handler: () => Promise<Response>): Promise<Response> => {
    const start = Date.now();
    const requestId = crypto.randomUUID();

    // Create child logger with request context
    const requestLogger = logger.child({ requestId });

    requestLogger.logRequest(request);

    try {
      const response = await handler();
      const duration = Date.now() - start;

      requestLogger.logResponse(response, duration);

      // Add request ID to response headers
      const newHeaders = new Headers(response.headers);
      newHeaders.set("x-request-id", requestId);

      return new Response(response.body, {
        status: response.status,
        statusText: response.statusText,
        headers: newHeaders,
      });
    } catch (error) {
      const duration = Date.now() - start;
      requestLogger.error("Request failed", { duration }, error as Error);
      throw error;
    }
  };
}
