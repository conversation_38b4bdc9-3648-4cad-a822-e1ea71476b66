{"name": "@repo/mailer", "version": "0.1.0", "type": "module", "private": true, "exports": {".": "./src/index.js", "./templates": "./src/templates/index.js"}, "scripts": {"dev": "tsc --watch", "build": "tsc", "check-types": "tsc --noEmit", "lint": "biome check .", "lint:fix": "biome check --write ."}, "dependencies": {"resend": "^4.0.1", "@repo/shared": "workspace:*", "@repo/config": "workspace:*"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@types/node": "^22.15.3", "typescript": "5.8.2"}}