import Stripe from "stripe";
import type {
  BillingProvider,
  CheckoutSession,
  Customer,
  Invoice,
  Price,
  Product,
  Subscription,
  WebhookEvent,
} from "../types.js";

/**
 * Stripe billing provider implementation
 */
export class StripeProvider implements BillingProvider {
  private stripe: Stripe;

  constructor(config: { apiKey: string; environment?: "test" | "live" }) {
    this.stripe = new Stripe(config.apiKey, {
      apiVersion: "2023-10-16",
      // For Cloudflare Workers compatibility
      httpClient: Stripe.createFetchHttpClient(),
    });
  }

  // Products and pricing
  async getProducts(): Promise<Product[]> {
    const products = await this.stripe.products.list({ active: true });
    return products.data.map(this.transformProduct);
  }

  async getProduct(productId: string): Promise<Product | null> {
    try {
      const product = await this.stripe.products.retrieve(productId);
      return this.transformProduct(product);
    } catch (error) {
      if ((error as Stripe.StripeRawError).code === "resource_missing") {
        return null;
      }
      throw error;
    }
  }

  async getPrices(productId?: string): Promise<Price[]> {
    const params: Stripe.PriceListParams = { active: true };
    if (productId) {
      params.product = productId;
    }

    const prices = await this.stripe.prices.list(params);
    return prices.data.map(this.transformPrice);
  }

  async getPrice(priceId: string): Promise<Price | null> {
    try {
      const price = await this.stripe.prices.retrieve(priceId);
      return this.transformPrice(price);
    } catch (error) {
      if ((error as Stripe.StripeRawError).code === "resource_missing") {
        return null;
      }
      throw error;
    }
  }

  // Customers
  async createCustomer(data: {
    email: string;
    name?: string;
    metadata?: Record<string, string>;
  }): Promise<Customer> {
    const customer = await this.stripe.customers.create(data);
    return this.transformCustomer(customer as Stripe.Customer);
  }

  async getCustomer(customerId: string): Promise<Customer | null> {
    try {
      const customer = await this.stripe.customers.retrieve(customerId);
      if (customer.deleted) return null;
      return this.transformCustomer(customer as Stripe.Customer);
    } catch (error) {
      if ((error as Stripe.StripeRawError).code === "resource_missing") {
        return null;
      }
      throw error;
    }
  }

  async updateCustomer(customerId: string, data: Partial<Customer>): Promise<Customer> {
    const customer = await this.stripe.customers.update(customerId, {
      email: data.email,
      name: data.name,
      metadata: data.metadata,
    });
    return this.transformCustomer(customer);
  }

  async deleteCustomer(customerId: string): Promise<void> {
    await this.stripe.customers.del(customerId);
  }

  // Subscriptions
  async createSubscription(data: {
    customerId: string;
    priceId: string;
    quantity?: number;
    metadata?: Record<string, string>;
    trialPeriodDays?: number;
  }): Promise<Subscription> {
    const subscription = await this.stripe.subscriptions.create({
      customer: data.customerId,
      items: [{ price: data.priceId, quantity: data.quantity || 1 }],
      metadata: data.metadata,
      trial_period_days: data.trialPeriodDays,
    });
    return this.transformSubscription(subscription);
  }

  async getSubscription(subscriptionId: string): Promise<Subscription | null> {
    try {
      const subscription = await this.stripe.subscriptions.retrieve(subscriptionId);
      return this.transformSubscription(subscription);
    } catch (error) {
      if ((error as Stripe.StripeRawError).code === "resource_missing") {
        return null;
      }
      throw error;
    }
  }

  async updateSubscription(
    subscriptionId: string,
    data: {
      priceId?: string;
      quantity?: number;
      metadata?: Record<string, string>;
    }
  ): Promise<Subscription> {
    const updateParams: Stripe.SubscriptionUpdateParams = {};

    if (data.metadata) {
      updateParams.metadata = data.metadata;
    }

    if (data.priceId || data.quantity) {
      const subscription = await this.stripe.subscriptions.retrieve(subscriptionId);
      const currentItem = subscription.items.data[0];

      if (!currentItem) {
        throw new Error("No subscription items found");
      }

      updateParams.items = [
        {
          id: currentItem.id,
          price: data.priceId || currentItem.price.id,
          quantity: data.quantity || currentItem.quantity,
        },
      ];
    }

    const subscription = await this.stripe.subscriptions.update(subscriptionId, updateParams);
    return this.transformSubscription(subscription);
  }

  async cancelSubscription(
    subscriptionId: string,
    cancelAtPeriodEnd = true
  ): Promise<Subscription> {
    const subscription = await this.stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: cancelAtPeriodEnd,
    });
    return this.transformSubscription(subscription);
  }

  // Invoices
  async getInvoices(customerId: string): Promise<Invoice[]> {
    const invoices = await this.stripe.invoices.list({ customer: customerId });
    return invoices.data.map(this.transformInvoice);
  }

  async getInvoice(invoiceId: string): Promise<Invoice | null> {
    try {
      const invoice = await this.stripe.invoices.retrieve(invoiceId);
      return this.transformInvoice(invoice);
    } catch (error) {
      if ((error as Stripe.StripeRawError).code === "resource_missing") {
        return null;
      }
      throw error;
    }
  }

  // Checkout
  async createCheckoutSession(data: {
    mode: "payment" | "subscription" | "setup";
    priceId?: string;
    customerId?: string;
    successUrl: string;
    cancelUrl: string;
    metadata?: Record<string, string>;
  }): Promise<CheckoutSession> {
    const params: Stripe.Checkout.SessionCreateParams = {
      mode: data.mode,
      success_url: data.successUrl,
      cancel_url: data.cancelUrl,
      metadata: data.metadata,
    };

    if (data.customerId) {
      params.customer = data.customerId;
    }

    if (data.priceId) {
      params.line_items = [{ price: data.priceId, quantity: 1 }];
    }

    const session = await this.stripe.checkout.sessions.create(params);
    return this.transformCheckoutSession(session);
  }

  // Customer portal
  async createCustomerPortalSession(
    customerId: string,
    returnUrl: string
  ): Promise<{ url: string }> {
    const session = await this.stripe.billingPortal.sessions.create({
      customer: customerId,
      return_url: returnUrl,
    });
    return { url: session.url };
  }

  // Webhooks
  async constructWebhookEvent(
    payload: string,
    signature: string,
    secret: string
  ): Promise<WebhookEvent> {
    const event = this.stripe.webhooks.constructEvent(payload, signature, secret);
    return {
      id: event.id,
      type: event.type,
      data: event.data,
      created: new Date(event.created * 1000),
    };
  }

  // Usage-based billing
  async reportUsage(subscriptionItemId: string, quantity: number, timestamp?: Date): Promise<void> {
    await this.stripe.subscriptionItems.createUsageRecord(subscriptionItemId, {
      quantity,
      timestamp: timestamp ? Math.floor(timestamp.getTime() / 1000) : undefined,
    });
  }

  // Transform functions
  private transformProduct(product: Stripe.Product): Product {
    return {
      id: product.id,
      name: product.name,
      description: product.description || undefined,
      active: product.active,
      metadata: product.metadata,
    };
  }

  private transformPrice(price: Stripe.Price): Price {
    return {
      id: price.id,
      productId: price.product as string,
      unitAmount: price.unit_amount || 0,
      currency: price.currency,
      interval: price.recurring?.interval,
      intervalCount: price.recurring?.interval_count,
      type: price.type === "recurring" ? "recurring" : "one_time",
      active: price.active,
      metadata: price.metadata,
    };
  }

  private transformCustomer(customer: Stripe.Customer): Customer {
    return {
      id: customer.id,
      email: customer.email || "",
      name: customer.name || undefined,
      metadata: customer.metadata,
    };
  }

  private transformSubscription(subscription: Stripe.Subscription): Subscription {
    return {
      id: subscription.id,
      customerId: subscription.customer as string,
      status: subscription.status as
        | "active"
        | "canceled"
        | "incomplete"
        | "incomplete_expired"
        | "past_due"
        | "trialing"
        | "unpaid"
        | "paused",
      currentPeriodStart: new Date(subscription.current_period_start * 1000),
      currentPeriodEnd: new Date(subscription.current_period_end * 1000),
      cancelAtPeriodEnd: subscription.cancel_at_period_end,
      canceledAt: subscription.canceled_at ? new Date(subscription.canceled_at * 1000) : undefined,
      trialStart: subscription.trial_start ? new Date(subscription.trial_start * 1000) : undefined,
      trialEnd: subscription.trial_end ? new Date(subscription.trial_end * 1000) : undefined,
      metadata: subscription.metadata,
      items: subscription.items.data.map((item) => ({
        id: item.id,
        priceId: item.price.id,
        quantity: item.quantity || 1,
      })),
    };
  }

  private transformInvoice(invoice: Stripe.Invoice): Invoice {
    return {
      id: invoice.id,
      customerId: invoice.customer as string,
      subscriptionId: invoice.subscription as string | undefined,
      status: invoice.status as "draft" | "open" | "paid" | "uncollectible" | "void",
      amount: invoice.amount_paid,
      currency: invoice.currency,
      created: new Date(invoice.created * 1000),
      dueDate: invoice.due_date ? new Date(invoice.due_date * 1000) : undefined,
      paidAt: invoice.status_transitions.paid_at
        ? new Date(invoice.status_transitions.paid_at * 1000)
        : undefined,
      metadata: invoice.metadata || undefined,
    };
  }

  private transformCheckoutSession(session: Stripe.Checkout.Session): CheckoutSession {
    return {
      id: session.id,
      url: session.url || "",
      customerId: session.customer as string | undefined,
      mode: session.mode,
      status: session.status as "complete" | "expired" | "open",
      successUrl: session.success_url || "",
      cancelUrl: session.cancel_url || "",
      metadata: session.metadata || undefined,
    };
  }
}
