{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "checkJs": false, "jsx": "preserve", "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitReturns": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "isolatedModules": true}, "include": ["src/**/*.ts"], "exclude": ["node_modules", "dist"]}