{"name": "@repo/auth", "version": "0.1.0", "type": "module", "private": true, "exports": {".": "./dist/index.js", "./config": "./dist/config/index.js", "./providers": "./dist/providers/index.js"}, "types": "./dist/index.d.ts", "scripts": {"dev": "tsc --watch", "build": "tsc", "check-types": "tsc --noEmit", "lint": "biome check .", "lint:fix": "biome check --write ."}, "dependencies": {"better-auth": "^1.2.10", "@repo/db": "workspace:*", "@repo/shared": "workspace:*", "@repo/config": "workspace:*"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@types/node": "^22.15.3", "typescript": "5.8.2"}}