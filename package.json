{"name": "sss", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev --concurrency 15", "lint": "biome check .", "lint:fix": "biome check --write .", "format": "biome format --write .", "check-types": "turbo run check-types", "deploy": "turbo run deploy --filter=web"}, "devDependencies": {"@biomejs/biome": "^2.0.6", "turbo": "^2.5.4", "typescript": "5.8.3", "wrangler": "^4.22.0"}, "packageManager": "pnpm@10.12.4", "engines": {"node": ">=18"}, "pnpm": {"overrides": {"esbuild": "^0.21.5"}}}