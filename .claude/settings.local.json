{"permissions": {"allow": ["Bash(pnpm list:*)", "Bash(rm:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(for pkg in db storage mailer auth config)", "Bash(do cp packages/shared/tsconfig.json packages/$pkg/tsconfig.json)", "Bash(done)", "Bash(ls:*)", "Bash(do cp packages/shared/tsconfig.json packages/$pkg/)", "Bash(pnpm install:*)", "Bash(pnpm turbo run:*)", "Bash(pnpm dev:*)", "Bash(find:*)", "Bash(pnpm lint:*)", "Bash(pnpm check-types:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(pnpm biome check:*)", "Bash(pnpm build)", "Bash(pnpm turbo build:*)", "Bash(pnpm run:*)"], "deny": []}}